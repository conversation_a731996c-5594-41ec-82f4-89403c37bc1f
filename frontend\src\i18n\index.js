import { createI18n } from 'vue-i18n'
import store from '../store'
import { getAppLocale, DEFAULT_LOCALE } from '@/utils/locale'

// 动态导入所有翻译文件
const loadLocaleMessages = () => {
  const locales = {
    zh_CN: {},
    en_US: {}
  }

  // 导入zh_CN下的所有JSON文件
  const zhModules = import.meta.glob('./zh_CN/**/*.json', { eager: true })
  for (const path in zhModules) {
    const module = zhModules[path].default
    // 合并翻译内容到locales.zh_CN，保持命名空间
    for (const key in module) {
      locales.zh_CN[key] = module[key]
    }
  }

  // 导入en_US下的所有JSON文件
  const enModules = import.meta.glob('./en_US/**/*.json', { eager: true })
  for (const path in enModules) {
    const module = enModules[path].default
    // 合并翻译内容到locales.en_US，保持命名空间
    for (const key in module) {
      locales.en_US[key] = module[key]
    }
  }

  return locales
}

// 从store中获取locale，确保一致性
const getLocale = () => {
  // 如果store已初始化，使用store中的locale
  if (store.state.locale) {
    return store.state.locale
  }
  // 否则使用智能语言检测
  return getAppLocale()
}

const i18n = createI18n({
  legacy: false, // 使用Composition API
  locale: getLocale(),
  fallbackLocale: DEFAULT_LOCALE, // 使用英文作为默认回退语言
  messages: loadLocaleMessages(),
  // 添加i18n ally扩展所需的配置
  silentTranslationWarn: true, // 在开发环境中禁用翻译警告
  missingWarn: false, // 禁用缺失翻译警告
  fallbackWarn: false, // 禁用回退翻译警告
})

export default i18n
