# 创建多语言翻译文件的PowerShell脚本

# 定义语言映射和基础翻译
$languages = @{
    "ko_KR" = @{
        "name" = "Korean"
        "translations" = @{
            "cancel" = "취소"
            "error" = "오류"
            "loading" = "로딩 중..."
            "success" = "성공"
            "webname" = "정량 거래"
            "submit" = "제출"
            "confirm" = "확인"
            "home" = "홈"
            "back" = "뒤로"
            "login" = "로그인"
            "logout" = "로그아웃"
            "register" = "회원가입"
            "username" = "사용자명"
            "password" = "비밀번호"
            "email" = "이메일"
            "phone" = "전화번호"
            "search" = "검색"
            "reset" = "재설정"
            "all" = "전체"
            "status" = "상태"
            "operation" = "작업"
            "noData" = "데이터 없음"
            "title" = "제목"
        }
    }
    "th_TH" = @{
        "name" = "Thai"
        "translations" = @{
            "cancel" = "ยกเลิก"
            "error" = "ข้อผิดพลาด"
            "loading" = "กำลังโหลด..."
            "success" = "สำเร็จ"
            "webname" = "การซื้อขายเชิงปริมาณ"
            "submit" = "ส่ง"
            "confirm" = "ยืนยัน"
            "home" = "หน้าแรก"
            "back" = "กลับ"
            "login" = "เข้าสู่ระบบ"
            "logout" = "ออกจากระบบ"
            "register" = "สมัครสมาชิก"
            "username" = "ชื่อผู้ใช้"
            "password" = "รหัสผ่าน"
            "email" = "อีเมล"
            "phone" = "เบอร์โทรศัพท์"
            "search" = "ค้นหา"
            "reset" = "รีเซ็ต"
            "all" = "ทั้งหมด"
            "status" = "สถานะ"
            "operation" = "การดำเนินการ"
            "noData" = "ไม่มีข้อมูล"
            "title" = "หัวข้อ"
        }
    }
    "ms_MY" = @{
        "name" = "Malay"
        "translations" = @{
            "cancel" = "Batal"
            "error" = "Ralat"
            "loading" = "Memuatkan..."
            "success" = "Berjaya"
            "webname" = "Perdagangan Kuantitatif"
            "submit" = "Hantar"
            "confirm" = "Sahkan"
            "home" = "Laman Utama"
            "back" = "Kembali"
            "login" = "Log Masuk"
            "logout" = "Log Keluar"
            "register" = "Daftar"
            "username" = "Nama Pengguna"
            "password" = "Kata Laluan"
            "email" = "E-mel"
            "phone" = "Nombor Telefon"
            "search" = "Cari"
            "reset" = "Set Semula"
            "all" = "Semua"
            "status" = "Status"
            "operation" = "Operasi"
            "noData" = "Tiada Data"
            "title" = "Tajuk"
        }
    }
    "zh_TW" = @{
        "name" = "Traditional Chinese (Taiwan)"
        "translations" = @{
            "cancel" = "取消"
            "error" = "錯誤"
            "loading" = "載入中..."
            "success" = "成功"
            "webname" = "量化交易"
            "submit" = "提交"
            "confirm" = "確認"
            "home" = "首頁"
            "back" = "返回"
            "login" = "登入"
            "logout" = "登出"
            "register" = "註冊"
            "username" = "使用者名稱"
            "password" = "密碼"
            "email" = "電子郵件"
            "phone" = "電話號碼"
            "search" = "搜尋"
            "reset" = "重置"
            "all" = "全部"
            "status" = "狀態"
            "operation" = "操作"
            "noData" = "暫無資料"
            "title" = "標題"
        }
    }
    "zh_HK" = @{
        "name" = "Traditional Chinese (Hong Kong)"
        "translations" = @{
            "cancel" = "取消"
            "error" = "錯誤"
            "loading" = "載入中..."
            "success" = "成功"
            "webname" = "量化交易"
            "submit" = "提交"
            "confirm" = "確認"
            "home" = "首頁"
            "back" = "返回"
            "login" = "登入"
            "logout" = "登出"
            "register" = "註冊"
            "username" = "使用者名稱"
            "password" = "密碼"
            "email" = "電子郵件"
            "phone" = "電話號碼"
            "search" = "搜尋"
            "reset" = "重置"
            "all" = "全部"
            "status" = "狀態"
            "operation" = "操作"
            "noData" = "暫無資料"
            "title" = "標題"
        }
    }
}

# 获取所有中文翻译文件
$zhFiles = Get-ChildItem -Path "frontend/src/i18n/zh_CN" -Recurse -Filter "*.json"

foreach ($lang in $languages.Keys) {
    Write-Host "Creating translation files for $lang..."

    foreach ($file in $zhFiles) {
        # 计算相对路径
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\frontend\src\i18n\zh_CN\", "")
        $targetPath = "frontend/src/i18n/$lang/$relativePath"
        $targetDir = Split-Path $targetPath -Parent

        # 创建目录
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }

        # 如果文件不存在，创建基础翻译文件
        if (!(Test-Path $targetPath)) {
            # 读取中文原文件
            $zhContent = Get-Content $file.FullName -Raw -Encoding UTF8 | ConvertFrom-Json

            # 创建基础翻译（暂时复制中文内容，后续可以替换）
            $translatedContent = $zhContent

            # 转换为JSON并保存
            $jsonContent = $translatedContent | ConvertTo-Json -Depth 10 -Compress:$false
            $jsonContent | Out-File -FilePath $targetPath -Encoding UTF8

            Write-Host "Created: $relativePath"
        }
    }
}

Write-Host "Translation files created for all languages."
