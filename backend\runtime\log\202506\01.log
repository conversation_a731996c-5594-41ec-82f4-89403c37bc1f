[2025-06-01T01:43:50+08:00][error] [10501]SQLSTATE[HY000] [2002] 由于目标计算机积极拒绝，无法连接。[D:\web\lianghua_int\backend\vendor\topthink\think-orm\src\db\PDOConnection.php:840]
[2025-06-01T01:43:55+08:00][error] [10501]SQLSTATE[HY000] [2002] 由于目标计算机积极拒绝，无法连接。[D:\web\lianghua_int\backend\vendor\topthink\think-orm\src\db\PDOConnection.php:840]
[2025-06-01T01:44:03+08:00][error] [10501]SQLSTATE[HY000] [2002] 由于目标计算机积极拒绝，无法连接。[D:\web\lianghua_int\backend\vendor\topthink\think-orm\src\db\PDOConnection.php:840]
[2025-06-01T01:44:40+08:00][sql] CONNECT:[ UseTime:0.003202s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T01:44:40+08:00][sql] SHOW FULL COLUMNS FROM `agent_level_config` [ RunTime:0.006366s ]
[2025-06-01T01:44:40+08:00][sql] SELECT `level`,`radio`,`invited_users` FROM `agent_level_config` ORDER BY `level` ASC [ RunTime:0.004152s ]
[2025-06-01T01:45:48+08:00][sql] CONNECT:[ UseTime:0.022464s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T01:45:48+08:00][sql] SHOW FULL COLUMNS FROM `agent_level_config` [ RunTime:0.001325s ]
[2025-06-01T01:45:48+08:00][sql] SELECT `level`,`radio`,`invited_users` FROM `agent_level_config` ORDER BY `level` ASC [ RunTime:0.000313s ]
[2025-06-01T01:46:01+08:00][sql] CONNECT:[ UseTime:0.013726s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T01:46:01+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.002584s ]
[2025-06-01T01:46:01+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.001552s ]
[2025-06-01T01:46:01+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.001614s ]
[2025-06-01T01:46:01+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.002054s ]
[2025-06-01T16:17:50+08:00][sql] CONNECT:[ UseTime:0.014444s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:17:50+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001836s ]
[2025-06-01T16:17:50+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000446s ]
[2025-06-01T16:17:50+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000710s ]
[2025-06-01T16:17:50+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000396s ]
[2025-06-01T16:18:02+08:00][sql] CONNECT:[ UseTime:0.008224s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:18:02+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001605s ]
[2025-06-01T16:18:02+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000391s ]
[2025-06-01T16:18:02+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000835s ]
[2025-06-01T16:18:02+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000566s ]
[2025-06-01T16:27:09+08:00][sql] CONNECT:[ UseTime:0.017969s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:27:09+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001377s ]
[2025-06-01T16:27:09+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000347s ]
[2025-06-01T16:27:09+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000717s ]
[2025-06-01T16:27:09+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000395s ]
[2025-06-01T16:27:23+08:00][sql] CONNECT:[ UseTime:0.022923s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:27:23+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001289s ]
[2025-06-01T16:27:23+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000337s ]
[2025-06-01T16:27:23+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000759s ]
[2025-06-01T16:27:23+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000393s ]
[2025-06-01T16:27:33+08:00][sql] CONNECT:[ UseTime:0.016220s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:27:33+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001267s ]
[2025-06-01T16:27:33+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000355s ]
[2025-06-01T16:27:33+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000679s ]
[2025-06-01T16:27:33+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000395s ]
[2025-06-01T16:27:46+08:00][sql] CONNECT:[ UseTime:0.020097s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:27:46+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001331s ]
[2025-06-01T16:27:46+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000371s ]
[2025-06-01T16:27:46+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000689s ]
[2025-06-01T16:27:46+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000397s ]
[2025-06-01T16:29:30+08:00][sql] CONNECT:[ UseTime:0.016404s ] mysql:host=localhost;port=3306;dbname=国际量化;charset=utf8mb4
[2025-06-01T16:29:30+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_symbol` [ RunTime:0.001486s ]
[2025-06-01T16:29:30+08:00][sql] SELECT * FROM `funding_rate_symbol` WHERE  `id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000349s ]
[2025-06-01T16:29:30+08:00][sql] SHOW FULL COLUMNS FROM `funding_rate_history` [ RunTime:0.000777s ]
[2025-06-01T16:29:30+08:00][sql] SELECT `calc_time`,`last_funding_rate`,`mark_price`,`funding_interval_hours` FROM `funding_rate_history` WHERE  `symbol_id` = 1  AND `calc_time` BETWEEN 1746115200 AND 1748793599  ORDER BY `calc_time` DESC [ RunTime:0.000417s ]
